{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "emitDeclarationOnly": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}